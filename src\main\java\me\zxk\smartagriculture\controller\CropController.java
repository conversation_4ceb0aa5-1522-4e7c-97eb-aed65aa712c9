package me.zxk.smartagriculture.controller;

import lombok.RequiredArgsConstructor;
import me.zxk.smartagriculture.common.ApiResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 作物控制器
 */
@RestController
@RequestMapping("/api/crops")
@RequiredArgsConstructor
public class CropController {

    /**
     * 获取作物选项列表
     */
    @GetMapping("/options")
    public ApiResponse<List<String>> getCropOptions() {
        List<String> crops = Arrays.asList(
                "番茄", "黄瓜", "茄子", "辣椒", "生菜", "菠菜", 
                "白菜", "萝卜", "胡萝卜", "豆角", "豌豆", "玉米"
        );
        return ApiResponse.success(crops);
    }
}
