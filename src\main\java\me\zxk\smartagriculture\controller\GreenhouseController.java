package me.zxk.smartagriculture.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import me.zxk.smartagriculture.common.ApiResponse;
import me.zxk.smartagriculture.dto.greenhouse.*;
import me.zxk.smartagriculture.service.GreenhouseService;
import me.zxk.smartagriculture.util.JwtUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * 大棚控制器
 */
@RestController
@RequestMapping("/api/greenhouses")
@RequiredArgsConstructor
public class GreenhouseController {

    private final GreenhouseService greenhouseService;
    private final JwtUtil jwtUtil;

    /**
     * 创建大棚
     */
    @PostMapping
    public ApiResponse<GreenhouseDetailResponse> createGreenhouse(
            @Valid @RequestBody CreateGreenhouseRequest request,
            HttpServletRequest httpRequest) {
        try {
            Long userId = getUserIdFromRequest(httpRequest);
            GreenhouseDetailResponse response = greenhouseService.createGreenhouse(userId, request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }

    /**
     * 获取大棚列表
     */
    @GetMapping
    public ApiResponse<Page<GreenhouseListResponse>> getGreenhouseList(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt,desc") String sort,
            HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);

            // 解析排序参数
            String[] sortParams = sort.split(",");
            String sortField = sortParams[0];
            Sort.Direction direction = sortParams.length > 1 && "asc".equalsIgnoreCase(sortParams[1])
                    ? Sort.Direction.ASC : Sort.Direction.DESC;

            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortField));
            Page<GreenhouseListResponse> response = greenhouseService.getGreenhouseList(userId, pageable);
            return ApiResponse.success(response);
        } catch (Exception e) {
            return ApiResponse.error(500, e.getMessage());
        }
    }

    /**
     * 获取大棚详情
     */
    @GetMapping("/{greenhouseId}")
    public ApiResponse<GreenhouseDetailResponse> getGreenhouseDetail(
            @PathVariable Long greenhouseId,
            HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            GreenhouseDetailResponse response = greenhouseService.getGreenhouseDetail(userId, greenhouseId);
            return ApiResponse.success(response);
        } catch (Exception e) {
            return ApiResponse.error(404, e.getMessage());
        }
    }

    /**
     * 更新大棚信息
     */
    @PutMapping("/{greenhouseId}")
    public ApiResponse<GreenhouseDetailResponse> updateGreenhouse(
            @PathVariable Long greenhouseId,
            @Valid @RequestBody UpdateGreenhouseRequest request,
            HttpServletRequest httpRequest) {
        try {
            Long userId = getUserIdFromRequest(httpRequest);
            GreenhouseDetailResponse response = greenhouseService.updateGreenhouse(userId, greenhouseId, request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }

    /**
     * 删除大棚
     */
    @DeleteMapping("/{greenhouseId}")
    public ApiResponse<Void> deleteGreenhouse(
            @PathVariable Long greenhouseId,
            HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            greenhouseService.deleteGreenhouse(userId, greenhouseId);
            return ApiResponse.success("大棚删除成功", null);
        } catch (Exception e) {
            return ApiResponse.error(404, e.getMessage());
        }
    }

    /**
     * 获取大棚传感器数据
     */
    @GetMapping("/{greenhouseId}/sensor-data")
    public ApiResponse<Page<SensorDataResponse>> getSensorData(
            @PathVariable Long greenhouseId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "recordedAt"));
            Page<SensorDataResponse> response = greenhouseService.getSensorData(
                    userId, greenhouseId, startTime, endTime, pageable);
            return ApiResponse.success(response);
        } catch (Exception e) {
            return ApiResponse.error(500, e.getMessage());
        }
    }

    /**
     * 从请求中获取用户ID
     */
    private Long getUserIdFromRequest(HttpServletRequest request) {
        String token = extractTokenFromRequest(request);
        return jwtUtil.getUserIdFromToken(token);
    }

    /**
     * 从请求中提取JWT Token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        throw new RuntimeException("Token not found");
    }
}
