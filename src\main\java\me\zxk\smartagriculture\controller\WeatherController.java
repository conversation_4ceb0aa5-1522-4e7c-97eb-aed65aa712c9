package me.zxk.smartagriculture.controller;

import lombok.RequiredArgsConstructor;
import me.zxk.smartagriculture.common.ApiResponse;
import me.zxk.smartagriculture.dto.weather.WeatherResponse;
import me.zxk.smartagriculture.service.WeatherService;
import org.springframework.web.bind.annotation.*;

/**
 * 天气控制器
 */
@RestController
@RequestMapping("/api/weather")
@RequiredArgsConstructor
public class WeatherController {

    private final WeatherService weatherService;

    /**
     * 获取天气信息
     */
    @GetMapping
    public ApiResponse<WeatherResponse> getWeatherInfo(
            @RequestParam Double latitude,
            @RequestParam Double longitude) {
        try {
            WeatherResponse response = weatherService.getWeatherInfo(latitude, longitude);
            return ApiResponse.success(response);
        } catch (Exception e) {
            return ApiResponse.error(503, "天气服务异常: " + e.getMessage());
        }
    }
}
